<?php

namespace App\Jobs;

use App\Events\ExternalDatabaseBusy;
use App\Events\ProductCreated;
use App\Events\ProductNotFound;
use App\Http\Client\OpenFoodFactsClient;
use App\Models\User;
use App\Repositories\ProductRepositoryInterface;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Queue\Queueable;
use Illuminate\Support\Facades\Log;

class FetchExternalProductJob implements ShouldQueue
{
    use Queueable;

    public $tries = 5;
    public $backoff = 30;

    /**
     * Create a new job instance.
     */
    public function __construct(public string $barcode, public User $user)
    {
    }

    /**
     * Execute the job.
     */
    public function handle(OpenFoodFactsClient $client, ProductRepositoryInterface $productRepository): void
    {
        try {
            $productData = $client->searchProduct($this->barcode);

            if ($productData) {
                $product = $productRepository->create($productData);
                broadcast(new ProductCreated($product));
            } else {
                // Si no se encuentra el producto, emitir evento específico
                broadcast(new ProductNotFound($this->barcode));
            }
        } catch (\Throwable $th) {
            // En caso de error, emitir evento de base de datos ocupada
            broadcast(new ExternalDatabaseBusy());
            dd($th->getMessage());
            // Log del error para debugging
            Log::error('Error fetching external product: '.$th->getMessage(), [
                'barcode' => $this->barcode,
                'user_id' => $this->user->id,
                'exception' => $th,
            ]);

            throw $th;
        }
    }
}
