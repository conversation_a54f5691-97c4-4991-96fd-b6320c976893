<?php

use Illuminate\Support\Facades\Broadcast;

Broadcast::channel('App.Models.User.{id}', function ($user, $id) {
    return (int) $user->id === (int) $id;
});

Broadcast::channel('product-created-external-{id}', function ($user, $id) {
    return (int) $user->id === (int) $id;
});

Broadcast::channel('external-database-busy-{id}', function ($user, $id) {
    return (int) $user->id === (int) $id;
});

Broadcast::channel('product-search-external-{id}', function ($user, $id) {
    return (int) $user->id === (int) $id;
});
