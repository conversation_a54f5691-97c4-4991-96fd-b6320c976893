<script setup lang="ts">
import { ScanLineIcon, XIcon, PlusIcon, SearchIcon } from 'lucide-vue-next';
import { onMounted, ref, watch } from 'vue';
import { useForm, usePage } from '@inertiajs/vue3';
import axios from 'axios';
import BarcodeReader from './BarcodeReader.vue';
import { useRadixToast } from '@/composables/useRadixToast';
import AddProductWithoutStockModal from './AddProductWithoutStockModal.vue';
import BatchManagementModal from './BatchManagementModal.vue';
import AddProductToLocalModal from './AddProductToLocalModal.vue';

interface Category {
    id: number;
    name: string;
}

interface Local {
    id: number;
    name: string;
}

interface Batch {
    id: number;
    barcode: string | null;
    expiration_date: string | null;
    quantity: number;
}

interface Product {
    id: number;
    name: string;
    description: string;
    barcode: string;
    category_id: number;
    expiration_date: string | null;
    category: {
        id: number;
        name: string;
    };
    batches?: Batch[];
    stock?: Array<{
        id: number;
        local_id: number;
        product_id: number;
        stock: number;
        price: number;
        min_stock: number;
        max_stock: number;
        expiration_date: string;
        status: boolean;
    }>;
}

const showScanner = ref(false);
const showAddProductModal = ref(false);
const showBatchModal = ref(false);
const showAddToLocalModal = ref(false);
const categories = ref<Category[]>([]);
const scannedCode = ref('');
const scannedCodes = ref<string[]>([]);
const searchBarcode = ref('');
const productFound = ref<Product | null>(null);
const isLoading = ref(false);
const useBatches = ref(false);
const batches = ref<Batch[]>([]);
const selectedLocal = ref<Local | null>(null);
const loadingMessage = ref('');

const page = usePage();

interface Auth {
    user: {
        id: number;
    };
}

const auth = page.props.auth as Auth;

const form = useForm({
    product_id: '',
    barcode: '',
    local_id: '',
    quantity: '',
    batch_id: '',
    movement_reason: 'Ingreso de stock',
});

const { showSuccess, showError } = useRadixToast();

Echo.private('product-created-external-' + auth.user.id)
    .listen('.product-created', (e: any) => {
        const product: Product = {
            id: e.product.id,
            name: e.product.name,
            description: e.product.description,
            barcode: e.product.barcode,
            category_id: e.product.category_id,
            expiration_date: e.product.expiration_date,
            category: e.product.category,
        }
        productFound.value = product;
        if (isLoading.value) {
            isLoading.value = false;
        }
        console.log('✅ Producto creado externamente:', e);
    })
    .error((error: any) => {
        console.error('❌ Error en canal product-created-external:', error);
    });


Echo.private('external-database-busy-' + auth.user.id)
    .listen('.external-database-busy', (e: any) => {
        console.log('⚠️ Base de datos externa ocupada:', e);
        if (isLoading.value) {
            loadingMessage.value = e.message || 'Base de datos externa ocupada, reintentando...';
        }
        showError('Fuente de datos externa ocupada, estamos reintentando encontrar tu producto.');
    })
    .error((error: any) => {
        console.error('❌ Error en canal external-database-busy:', error);
    });

Echo.private('product-search-external-' + auth.user.id)
    .listen('.product-not-found', (e: any) => {
        console.log('❌ Producto no encontrado en base de datos externa:', e);
        if (isLoading.value) {
            loadingMessage.value = e.message || 'Producto no encontrado en la base de datos externa';
            // Mantener el loading por un momento para mostrar el mensaje
            setTimeout(() => {
                isLoading.value = false;
                loadingMessage.value = '';
            }, 2000);
        }
        showError('No hemos encontrado el producto en nuestras fuentes de datos, puede crearlo manualmente.');
    })
    .listen('.product-already-exist', (e: any) => {
        console.log('❌ Producto ya existe en la base de datos:', e);
        if (isLoading.value) {
            loadingMessage.value = e.message || 'Producto ya existe en la base de datos';
            // Mantener el loading por un momento para mostrar el mensaje
            setTimeout(() => {
                isLoading.value = false;
                loadingMessage.value = '';
            }, 2000);
        }
    })
    .error((error: any) => {
        console.error('❌ Error en canal product-search-external:', error);
    });


onMounted(async () => {
    try {
        const response = await fetch('/categories');
        const data = await response.json();
        categories.value = data.data;
    } catch (error) {
        console.error(error);
    }
});

const emits = defineEmits(['close']);

const searchProduct = async () => {
    if (!searchBarcode.value && !scannedCode.value) {
        showError('Ingrese un código de barras para buscar');
        return;
    }

    const barcodeToSearch = searchBarcode.value || scannedCode.value;
    isLoading.value = true;
    loadingMessage.value = 'Buscando producto...';

    try {
        const response = await fetch(`/api/products/barcode/${barcodeToSearch}`);
        const data = await response.json();

        if (response.status === 200) {
            productFound.value = data;
            form.product_id = data.id;
            form.barcode = data.barcode;

            // Si el producto tiene batches, cargarlos
            if (data.batches && data.batches.length > 0) {
                batches.value = data.batches;
            } else {
                batches.value = [];
            }

            isLoading.value = false;
            loadingMessage.value = '';
            showSuccess('Producto encontrado' + data.name);
        }

        if (response.status === 404) {
            productFound.value = null;
            showError(data.message);

        }

    } catch (error) {
        console.error('Error buscando producto:', error);
        showError('Error al buscar el producto');
        isLoading.value = false;
        loadingMessage.value = '';
    }
};

const checkProductInLocal = (localId: string) => {
    if (!productFound.value) return;

    // Find the selected local
    const locals = page.props.locals as Local[];
    const local = locals.find(l => l.id === parseInt(localId));
    if (!local) return;

    // Check if product has stock information for this local
    const hasStockInLocal = productFound.value.stock &&
        productFound.value.stock.some((stock: any) => stock.local_id === parseInt(localId));

    if (!hasStockInLocal) {
        selectedLocal.value = local;
        showAddToLocalModal.value = true;
    }
};

watch(scannedCode, (newValue) => {
    if (newValue) {
        searchBarcode.value = newValue;
        searchProduct();
    }
});

// Watch for local selection changes
watch(() => form.local_id, (newLocalId) => {
    if (newLocalId && productFound.value) {
        checkProductInLocal(newLocalId);
    }
});

const handleSubmit = async () => {
    if (!productFound.value) {
        showError('Debe buscar y seleccionar un producto primero');
        return;
    }

    if (!form.local_id) {
        showError('Debe seleccionar un local');
        return;
    }

    if (!form.quantity || parseInt(form.quantity) <= 0) {
        showError('Debe ingresar una cantidad válida mayor a 0');
        return;
    }

    if (!form.movement_reason.trim()) {
        showError('Debe ingresar un motivo para el movimiento');
        return;
    }

    isLoading.value = true;

    try {
        const response = await axios.post(route('stock.entry'), {
            product_id: productFound.value.id,
            local_id: parseInt(form.local_id),
            quantity: parseInt(form.quantity),
            movement_reason: form.movement_reason.trim(),
            batch_id: form.batch_id ? parseInt(form.batch_id) : null,
        });

        const data = response.data;

        if (data.success) {
            showSuccess(data.message || 'Stock ingresado correctamente');

            // Log the successful operation for debugging
            console.log('Stock entry successful:', {
                stock_movement: data.data?.stock_movement,
                new_stock: data.data?.new_stock,
                local_product: data.data?.local_product,
            });

            resetForm();
        } else {
            throw new Error(data.message || 'Error al ingresar el stock');
        }
    } catch (error: any) {
        console.error('Stock entry error:', error);

        let errorMessage = 'Error al ingresar el stock';

        if (error.response?.data?.message) {
            errorMessage = error.response.data.message;
        } else if (error.response?.data?.errors) {
            // Handle Laravel validation errors
            const errors = error.response.data.errors;
            const firstError = Object.values(errors)[0];
            errorMessage = Array.isArray(firstError) ? firstError[0] : firstError;
        } else if (error.message) {
            errorMessage = error.message;
        }

        showError(errorMessage);
    } finally {
        isLoading.value = false;
    }
};

const resetForm = () => {
    form.reset();
    productFound.value = null;
    searchBarcode.value = '';
    scannedCode.value = '';
    useBatches.value = false;
    batches.value = [];
    loadingMessage.value = '';
};

const closeAddProductModal = () => {
    showAddProductModal.value = false;
};

const onProductCreated = (product: Product) => {
    productFound.value = product;
    form.product_id = String(product.id);
    form.barcode = product.barcode;
    showAddProductModal.value = false;
    showSuccess('Producto creado correctamente');
};

const closeBatchModal = () => {
    showBatchModal.value = false;
};

const onBatchCreated = (batch: Batch) => {
    batches.value.push(batch);
    form.batch_id = String(batch.id);
    showBatchModal.value = false;
    showSuccess('Lote creado correctamente');
};

const closeAddToLocalModal = () => {
    showAddToLocalModal.value = false;
};

const onProductAddedToLocal = (updatedProduct: Product) => {
    productFound.value = updatedProduct;
    showAddToLocalModal.value = false;
    showSuccess('Producto agregado al local correctamente');
};
</script>

<template>
    <div class="rounded-lg border bg-card shadow-sm">
        <div class="p-6">
            <div class="flex items-center justify-between mb-4">
                <h3 class="text-lg font-semibold">Ingreso de Stock</h3>
                <button @click="emits('close')" class="rounded-full p-2 hover:bg-muted/50">
                    <XIcon class="h-4 w-4" />
                </button>
            </div>

            <!-- Barcode Scanner and Search Section -->
            <div class="mb-6">
                <div class="flex items-center gap-2 mb-4">
                    <button @click="showScanner = !showScanner"
                        class="inline-flex items-center justify-center gap-2 rounded-md border border-input bg-background px-3 py-2 text-sm hover:bg-accent hover:text-accent-foreground">
                        <ScanLineIcon class="h-4 w-4" />
                        {{ showScanner ? 'Ocultar Scanner' : 'Escanear Código' }}
                    </button>
                    <div class="flex-1 flex gap-2">
                        <input v-model="searchBarcode" type="text" placeholder="Ingrese código de barras"
                            class="flex h-10 w-5/6 rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50" />
                        <button @click="searchProduct" :disabled="isLoading"
                            class="inline-flex items-center justify-center gap-2 rounded-md border border-input bg-background px-3 py-2 text-sm hover:bg-accent hover:text-accent-foreground disabled:opacity-50 disabled:cursor-not-allowed">
                            <SearchIcon v-if="!isLoading" class="h-4 w-4" />
                            <div v-if="isLoading"
                                class="h-4 w-4 border-2 border-current border-t-transparent rounded-full animate-spin">
                            </div>
                            {{ isLoading ? 'Buscando...' : 'Buscar Producto' }}
                        </button>
                    </div>
                    <button @click="showAddProductModal = true"
                        class="inline-flex items-center justify-center gap-2 rounded-md border border-input bg-background px-3 py-2 text-sm hover:bg-accent hover:text-accent-foreground">
                        <PlusIcon class="h-4 w-4" />
                        Nuevo Producto
                    </button>
                </div>

                <!-- Scanner View -->
                <div v-if="showScanner" class="border rounded-lg p-4">
                    <!-- Mobile-first layout -->
                    <div class="grid grid-rows-1 xl:grid-cols-2 gap-4">
                        <!-- Camera feed takes full width on mobile -->
                        <div class="w-full bg-black rounded-lg flex items-center justify-center">
                            <BarcodeReader @decode="(code: string) => {
                                scannedCode = code;
                                scannedCodes.push(code);
                            }" />
                        </div>

                        <!-- Scanned items below camera on mobile -->
                        <div class="space-y-2">
                            <div class="flex items-center justify-between">
                                <h4 class="font-medium">Códigos escaneados</h4>
                                <span class="text-sm text-muted-foreground">
                                    {{ scannedCodes.length }} en total
                                </span>
                            </div>
                            <div class="space-y-2 max-h-[200px] overflow-y-auto">
                                <div v-for="code in scannedCodes" :key="code"
                                    class="flex items-center justify-between p-2 bg-muted rounded-md">
                                    <span class="text-sm font-mono">{{ code }}</span>
                                    <button class="text-muted-foreground hover:text-destructive">
                                        <XIcon class="h-4 w-4" />
                                    </button>
                                </div>
                                <div v-if="scannedCodes.length === 0"
                                    class="text-sm text-muted-foreground text-center py-4">
                                    No hay códigos escaneados
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Loading Skeleton for Product Search -->
            <div v-if="isLoading && !productFound" class="mb-6 p-4 border rounded-lg bg-muted/30">
                <!-- Loading Message -->
                <div v-if="loadingMessage" class="mb-4 p-3 bg-blue-50 border border-blue-200 rounded-lg">
                    <div class="flex items-center gap-2">
                        <div class="h-4 w-4 border-2 border-blue-500 border-t-transparent rounded-full animate-spin">
                        </div>
                        <p class="text-sm text-blue-700 font-medium">{{ loadingMessage }}</p>
                    </div>
                </div>

                <!-- Skeleton Animation -->
                <div class="animate-pulse">
                    <div class="h-5 bg-muted rounded w-48 mb-4"></div>
                    <div class="grid grid-cols-2 gap-4">
                        <div>
                            <div class="h-4 bg-muted rounded w-16 mb-2"></div>
                            <div class="h-5 bg-muted rounded w-32"></div>
                        </div>
                        <div>
                            <div class="h-4 bg-muted rounded w-16 mb-2"></div>
                            <div class="h-5 bg-muted rounded w-28"></div>
                        </div>
                        <div>
                            <div class="h-4 bg-muted rounded w-20 mb-2"></div>
                            <div class="h-5 bg-muted rounded w-24"></div>
                        </div>
                        <div>
                            <div class="h-4 bg-muted rounded w-32 mb-2"></div>
                            <div class="h-5 bg-muted rounded w-20"></div>
                        </div>
                    </div>
                    <div class="mt-4">
                        <div class="h-4 bg-muted rounded w-24 mb-2"></div>
                        <div class="h-4 bg-muted rounded w-full mb-1"></div>
                        <div class="h-4 bg-muted rounded w-3/4"></div>
                    </div>
                </div>
            </div>

            <!-- Product Info Section -->
            <div v-if="productFound && !isLoading" class="mb-6 p-4 border rounded-lg bg-muted/30">
                <h4 class="font-medium mb-2">Información del Producto</h4>
                <div class="grid grid-cols-2 gap-4">
                    <div>
                        <p class="text-sm text-muted-foreground">Nombre:</p>
                        <p class="font-medium">{{ productFound.name }}</p>
                    </div>
                    <div>
                        <p class="text-sm text-muted-foreground">Código:</p>
                        <p class="font-medium">{{ productFound.barcode }}</p>
                    </div>
                    <div>
                        <p class="text-sm text-muted-foreground">Categoría:</p>
                        <p class="font-medium">{{ productFound.category?.name }}</p>
                    </div>
                    <div v-if="productFound.expiration_date">
                        <p class="text-sm text-muted-foreground">Fecha de Vencimiento:</p>
                        <p class="font-medium">{{ productFound.expiration_date }}</p>
                    </div>
                </div>
                <div class="mt-2">
                    <p class="text-sm text-muted-foreground">Descripción:</p>
                    <p class="text-sm">{{ productFound.description }}</p>
                </div>
            </div>

            <form @submit.prevent="handleSubmit" class="space-y-4">
                <div class="grid gap-4 items-center sm:grid-cols-2">
                    <!-- Formulario para ingreso de stock -->
                    <div class="space-y-2">
                        <label
                            class="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70">
                            Local
                        </label>
                        <select v-model="form.local_id" required
                            class="flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50">
                            <option value="">Selecciona un local</option>
                            <option v-for="local in page.props.locals as Local[]" :key="local.id" :value="local.id">
                                {{ local.name }}
                            </option>
                        </select>
                        <span class="text-sm text-red-600 dark:text-red-500">{{ form.errors.local_id }}</span>
                    </div>

                    <div class="space-y-2">
                        <label
                            class="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70">
                            Cantidad
                        </label>
                        <input v-model="form.quantity" type="number" step="1" min="1" required
                            class="flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50" />
                        <span class="text-sm text-red-600 dark:text-red-500">{{ form.errors.quantity }}</span>
                    </div>

                    <!-- Batch Management Section -->
                    <div class="col-span-2 space-y-2">
                        <div class="flex items-center justify-between">
                            <div class="flex items-center gap-2">
                                <input v-model="useBatches" type="checkbox" id="useBatches"
                                    class="h-4 w-4 rounded border-input bg-background text-primary focus:outline-none focus:ring-1 focus:ring-ring" />
                                <label for="useBatches" class="text-sm font-medium">
                                    Gestionar por lotes
                                </label>
                            </div>
                            <button v-if="useBatches" type="button" @click="showBatchModal = true"
                                class="inline-flex items-center justify-center gap-2 rounded-md border border-input bg-background px-3 py-2 text-sm hover:bg-accent hover:text-accent-foreground">
                                <PlusIcon class="h-4 w-4" />
                                Nuevo Lote
                            </button>
                        </div>

                        <div v-if="useBatches && batches.length > 0" class="border rounded-lg p-4 mt-2">
                            <h4 class="font-medium mb-2">Lotes disponibles</h4>
                            <div class="space-y-2 max-h-[200px] overflow-y-auto">
                                <div v-for="batch in batches" :key="batch.id"
                                    class="flex items-center justify-between p-2 bg-muted rounded-md"
                                    :class="{ 'border-2 border-primary': form.batch_id === String(batch.id) }">
                                    <div>
                                        <p class="text-sm font-medium">Lote #{{ batch.id }}</p>
                                        <p v-if="batch.expiration_date" class="text-xs text-muted-foreground">
                                            Vence: {{ batch.expiration_date }}
                                        </p>
                                        <p v-if="batch.barcode" class="text-xs font-mono">{{ batch.barcode }}</p>
                                    </div>
                                    <button type="button" @click="form.batch_id = String(batch.id)"
                                        class="inline-flex items-center justify-center rounded-md border border-input bg-background px-2 py-1 text-xs hover:bg-accent hover:text-accent-foreground">
                                        Seleccionar
                                    </button>
                                </div>
                            </div>
                        </div>

                        <div v-else-if="useBatches"
                            class="text-sm text-muted-foreground text-center py-4 border rounded-lg">
                            No hay lotes disponibles para este producto
                        </div>
                    </div>

                    <div class="col-span-2 space-y-2">
                        <label
                            class="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70">
                            Motivo del movimiento
                        </label>
                        <textarea v-model="form.movement_reason" rows="2" required
                            class="flex w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50" />
                        <span class="text-sm text-red-600 dark:text-red-500">{{ form.errors.movement_reason }}</span>
                    </div>
                </div>

                <div class="flex justify-end space-x-2">
                    <button type="button" @click="resetForm"
                        class="inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 border border-input bg-background hover:bg-accent hover:text-accent-foreground h-10 px-4 py-2">
                        Limpiar
                    </button>
                    <button type="submit" :disabled="isLoading || !productFound"
                        class="inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 bg-primary text-primary-foreground hover:bg-primary/90 h-10 px-4 py-2">
                        {{ isLoading ? 'Procesando...' : 'Registrar Ingreso' }}
                    </button>
                </div>
            </form>
        </div>
    </div>

    <!-- Modal para agregar producto sin stock -->
    <AddProductWithoutStockModal v-if="showAddProductModal" @close="closeAddProductModal"
        @product-created="onProductCreated" />

    <!-- Modal para gestionar lotes -->
    <BatchManagementModal v-if="showBatchModal" @close="closeBatchModal" @batch-created="onBatchCreated"
        :product-id="productFound?.id" />

    <!-- Modal para agregar producto al local -->
    <AddProductToLocalModal v-if="showAddToLocalModal && productFound && selectedLocal" @close="closeAddToLocalModal"
        @product-added-to-local="onProductAddedToLocal" :product="productFound" :local="selectedLocal" />
</template>
