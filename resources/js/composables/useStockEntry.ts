import { ref } from 'vue';
import { useForm } from '@inertiajs/vue3';
import axios from 'axios';
import { useRadixToast } from './useRadixToast';

interface Product {
    id: number;
    name: string;
    barcode: string;
}

interface StockEntryData {
    product_id: number;
    local_id: number;
    quantity: number;
    movement_reason: string;
    batch_id?: number | null;
}

export function useStockEntry() {
    const isSubmitting = ref(false);
    const { showSuccess, showError } = useRadixToast();

    const form = useForm({
        product_id: '',
        barcode: '',
        local_id: '',
        quantity: '',
        batch_id: '',
        movement_reason: 'Ingreso de stock',
    });

    const validateForm = (product: Product | null): string | null => {
        if (!product) {
            return 'Debe buscar y seleccionar un producto primero';
        }

        if (!form.local_id) {
            return 'Debe seleccionar un local';
        }

        if (!form.quantity || parseInt(form.quantity) <= 0) {
            return 'Debe ingresar una cantidad válida mayor a 0';
        }

        if (!form.movement_reason.trim()) {
            return 'Debe ingresar un motivo para el movimiento';
        }

        return null;
    };

    const submitStockEntry = async (product: Product): Promise<boolean> => {
        const validationError = validateForm(product);
        if (validationError) {
            showError(validationError);
            return false;
        }

        isSubmitting.value = true;

        try {
            const stockEntryData: StockEntryData = {
                product_id: product.id,
                local_id: parseInt(form.local_id),
                quantity: parseInt(form.quantity),
                movement_reason: form.movement_reason.trim(),
                batch_id: form.batch_id ? parseInt(form.batch_id) : null,
            };

            const response = await axios.post(route('stock.entry'), stockEntryData);
            const data = response.data;

            if (data.success) {
                showSuccess(data.message || 'Stock ingresado correctamente');
                
                console.log('Stock entry successful:', {
                    stock_movement: data.data?.stock_movement,
                    new_stock: data.data?.new_stock,
                    local_product: data.data?.local_product,
                });

                return true;
            } else {
                throw new Error(data.message || 'Error al ingresar el stock');
            }
        } catch (error: any) {
            console.error('Stock entry error:', error);

            let errorMessage = 'Error al ingresar el stock';

            if (error.response?.data?.message) {
                errorMessage = error.response.data.message;
            } else if (error.response?.data?.errors) {
                const errors = error.response.data.errors;
                const firstError = Object.values(errors)[0];
                errorMessage = Array.isArray(firstError) ? firstError[0] : firstError;
            } else if (error.message) {
                errorMessage = error.message;
            }

            showError(errorMessage);
            return false;
        } finally {
            isSubmitting.value = false;
        }
    };

    const resetForm = () => {
        form.reset();
    };

    return {
        form,
        isSubmitting,
        submitStockEntry,
        resetForm,
    };
}
