import { onMounted, onUnmounted, type Ref } from 'vue';

interface Product {
    id: number;
    name: string;
    description: string;
    barcode: string;
    category_id: number;
    expiration_date: string | null;
    category: {
        id: number;
        name: string;
    };
}

interface UseWebSocketEventsOptions {
    productFound: Ref<Product | null>;
    isLoading: Ref<boolean>;
    setLoadingMessage: (message: string) => void;
}

export function useWebSocketEvents({ 
    productFound, 
    isLoading, 
    setLoadingMessage 
}: UseWebSocketEventsOptions) {
    let productCreatedChannel: any;
    let databaseBusyChannel: any;
    let productSearchChannel: any;

    const setupWebSocketListeners = () => {
        // Product created externally
        productCreatedChannel = Echo.channel('product-created-external')
            .listen('.product-created', (e: any) => {
                const product: Product = {
                    id: e.product.id,
                    name: e.product.name,
                    description: e.product.description,
                    barcode: e.product.barcode,
                    category_id: e.product.category_id,
                    expiration_date: e.product.expiration_date,
                    category: e.product.category,
                };
                productFound.value = product;
                console.log('✅ Producto creado externamente:', e);
            })
            .error((error: any) => {
                console.error('❌ Error en canal product-created-external:', error);
            });

        // External database busy
        databaseBusyChannel = Echo.channel('external-database-busy')
            .listen('.external-database-busy', (e: any) => {
                console.log('⚠️ Base de datos externa ocupada:', e);
                if (isLoading.value) {
                    setLoadingMessage(e.message || 'Base de datos externa ocupada, reintentando...');
                }
            })
            .error((error: any) => {
                console.error('❌ Error en canal external-database-busy:', error);
            });

        // Product not found
        productSearchChannel = Echo.channel('product-search-external')
            .listen('.product-not-found', (e: any) => {
                console.log('❌ Producto no encontrado en base de datos externa:', e);
                if (isLoading.value) {
                    setLoadingMessage(e.message || 'Producto no encontrado en la base de datos externa');
                    // Mantener el loading por un momento para mostrar el mensaje
                    setTimeout(() => {
                        isLoading.value = false;
                        setLoadingMessage('');
                    }, 2000);
                }
            })
            .error((error: any) => {
                console.error('❌ Error en canal product-search-external:', error);
            });
    };

    const cleanupWebSocketListeners = () => {
        if (productCreatedChannel) {
            Echo.leave('product-created-external');
        }
        if (databaseBusyChannel) {
            Echo.leave('external-database-busy');
        }
        if (productSearchChannel) {
            Echo.leave('product-search-external');
        }
    };

    onMounted(() => {
        setupWebSocketListeners();
    });

    onUnmounted(() => {
        cleanupWebSocketListeners();
    });

    return {
        setupWebSocketListeners,
        cleanupWebSocketListeners,
    };
}
